{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Navbar.vue?vue&type=template&id=d16d6306&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Navbar.vue", "mtime": 1754536921924}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n  <div class=\"navbar\">\n    <hamburger\n      id=\"hamburger-container\"\n      :is-active=\"sidebar.opened\"\n      class=\"hamburger-container\"\n      @toggleClick=\"toggleSideBar\"\n    />\n    <breadcrumb id=\"breadcrumb-container\" class=\"breadcrumb-container\" />\n    <div class=\"right-menu\">\n      <template v-if=\"device !== 'mobile'\">\n<!--         <search id=\"header-search\" class=\"right-menu-item\" />--> \n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\" />\n      </template>\n      <el-dropdown\n        class=\"avatar-container right-menu-item hover-effect\"\n        trigger=\"click\"\n      >\n        <div class=\"avatar-wrapper\">\n          语言：{{ langLabel(nowLanguage)\n          }}<i class=\"el-icon-arrow-down el-icon--right\"></i>\n        </div>\n        <el-dropdown-menu slot=\"dropdown\">\n          <template v-for=\"lang in availableLanguages\">\n            <el-dropdown-item\n              :key=\"lang\"\n              v-if=\"lang !== nowLanguage\"\n              @click.native=\"toggleLang(lang)\"\n            >\n              {{ langLabel(lang) }}\n            </el-dropdown-item>\n          </template>\n        </el-dropdown-menu>\n      </el-dropdown>\n      <el-dropdown\n        class=\"avatar-container right-menu-item hover-effect\"\n        trigger=\"click\"\n      >\n        <div class=\"avatar-wrapper\">\n          {{ JavaInfo.realName\n          }}<i class=\"el-icon-arrow-down el-icon--right\"></i>\n        </div>\n        <el-dropdown-menu slot=\"dropdown\">\n          <router-link to=\"/\">\n            <el-dropdown-item>{{ $t(\"navbar.home\") }}</el-dropdown-item>\n          </router-link>\n          <router-link :to=\"{ path: '/maintain/user' }\" v-if=\"!isPhone\">\n            <el-dropdown-item>{{\n              $t(\"navbar.profile\")\n            }}</el-dropdown-item>\n          </router-link>\n\n          <el-dropdown-item @click.native=\"logout\">\n            <span>{{ $t(\"navbar.logout\") }}</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div>\n  </div>\n", null]}
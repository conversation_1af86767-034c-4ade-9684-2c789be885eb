package com.genco.common.model.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户拉新奖励配置表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_user_referral_reward_config")
@ApiModel(value = "UserReferralRewardConfig对象", description = "用户拉新奖励配置表")
public class UserReferralRewardConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户ID，关联eb_user.uid")
    private Integer uid;

    @ApiModelProperty(value = "拉新数量要求")
    private Integer referralCount;

    @ApiModelProperty(value = "首单数量要求")
    private Integer firstOrderCount;

    @ApiModelProperty(value = "奖励金额（印尼盾）")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;

    @Deprecated
    @ApiModelProperty(value = "已兑换的邀请人数（已废弃，请使用任务兑换记录表统计）")
    private Integer redeemedReferralCount;

    @Deprecated
    @ApiModelProperty(value = "已兑换的首单数量（已废弃，请使用任务兑换记录表统计）")
    private Integer redeemedFirstOrderCount;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

package com.genco.common.model.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户任务兑换记录表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_user_task_claim_record")
@ApiModel(value = "UserTaskClaimRecord", description = "用户任务兑换记录表")
public class UserTaskClaimRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Integer uid;

    @ApiModelProperty(value = "任务类型")
    private String taskType;

    @ApiModelProperty(value = "任务配置快照")
    private String taskConfigSnapshot;

    @ApiModelProperty(value = "本次兑换组数")
    private Integer claimGroups;

    @ApiModelProperty(value = "累计兑换组数")
    private Integer totalClaimGroups;

    @ApiModelProperty(value = "单组所需邀请人数")
    private Integer referralCountRequired;

    @ApiModelProperty(value = "单组所需首单人数")
    private Integer firstOrderCountRequired;

    @ApiModelProperty(value = "本次消耗邀请人数")
    private Integer referralCountUsed;

    @ApiModelProperty(value = "本次消耗首单人数")
    private Integer firstOrderCountUsed;

    @ApiModelProperty(value = "单组奖励金额")
    private BigDecimal rewardAmountPerGroup;

    @ApiModelProperty(value = "本次总奖励金额")
    private BigDecimal totalRewardAmount;

    @ApiModelProperty(value = "关联账单ID")
    private Integer billId;

    @ApiModelProperty(value = "兑换时间")
    private Date claimTime;

    @ApiModelProperty(value = "状态：1-有效，0-无效")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

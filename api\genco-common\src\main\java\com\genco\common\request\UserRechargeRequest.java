package com.genco.common.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 充值
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "UserRechargeRequest对象", description = "充值")
public class UserRechargeRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "充值金额")
    @DecimalMin(value = "1", message = "充值金额不能小于1")
    private BigDecimal price;

    @ApiModelProperty(value = "选择金额组合数据id")
    @JsonProperty(value = "recharge_id")
    private Integer groupDataId = 0;

    @ApiModelProperty(value = "充值类型| agent,partner")
    private String rechargeType;

    @ApiModelProperty(value = "支付渠道| xendit,haipay")
    private String payType = "xendit";

    @ApiModelProperty(value = "客户端ip")
    @JsonIgnore
    private String clientIp;

    @ApiModelProperty(value = "用户id")
    @JsonIgnore
    private Integer userId;
}

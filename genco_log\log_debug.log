{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.129",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/D:/Java/jre/lib/charsets.jar, file:/D:/Java/jre/lib/deploy.jar, file:/D:/Java/jre/lib/ext/access-bridge-64.jar, file:/D:/Java/jre/lib/ext/cldrdata.jar, file:/D:/Java/jre/lib/ext/dnsns.jar, file:/D:/Java/jre/lib/ext/jaccess.jar, file:/D:/Java/jre/lib/ext/localedata.jar, file:/D:/Java/jre/lib/ext/nashorn.jar, file:/D:/Java/jre/lib/ext/sunec.jar, file:/D:/Java/jre/lib/ext/sunjce_provider.jar, file:/D:/Java/jre/lib/ext/sunmscapi.jar, file:/D:/Java/jre/lib/ext/sunpkcs11.jar, file:/D:/Java/jre/lib/ext/zipfs.jar, file:/D:/Java/jre/lib/javaws.jar, file:/D:/Java/jre/lib/jce.jar, file:/D:/Java/jre/lib/jfr.jar, file:/D:/Java/jre/lib/jsse.jar, file:/D:/Java/jre/lib/management-agent.jar, file:/D:/Java/jre/lib/plugin.jar, file:/D:/Java/jre/lib/resources.jar, file:/D:/Java/jre/lib/rt.jar, file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/, file:/C:/Users/<USER>/Desktop/shop/api/genco-service/target/classes/, file:/C:/Users/<USER>/Desktop/shop/api/genco-common/target/classes/, file:/D:/MavenRepository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/D:/MavenRepository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/D:/MavenRepository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/D:/MavenRepository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/D:/MavenRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/D:/MavenRepository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/D:/MavenRepository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/D:/MavenRepository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/D:/MavenRepository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/MavenRepository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/D:/MavenRepository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/D:/MavenRepository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/D:/MavenRepository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/D:/MavenRepository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/MavenRepository/com/google/guava/guava/20.0/guava-20.0.jar, file:/D:/MavenRepository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/MavenRepository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/D:/MavenRepository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/MavenRepository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/MavenRepository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/D:/MavenRepository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/D:/MavenRepository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/MavenRepository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/MavenRepository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/D:/MavenRepository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/D:/MavenRepository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/D:/MavenRepository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/D:/MavenRepository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/D:/MavenRepository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/D:/MavenRepository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/D:/MavenRepository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/D:/MavenRepository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/D:/MavenRepository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/D:/MavenRepository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/D:/MavenRepository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/D:/MavenRepository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/D:/MavenRepository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/D:/MavenRepository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/D:/MavenRepository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/D:/MavenRepository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/D:/MavenRepository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/D:/MavenRepository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/D:/MavenRepository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/MavenRepository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/D:/MavenRepository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/D:/MavenRepository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/D:/MavenRepository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/D:/MavenRepository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/D:/MavenRepository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/D:/MavenRepository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/D:/MavenRepository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/D:/MavenRepository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/D:/MavenRepository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/D:/MavenRepository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/D:/MavenRepository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/D:/MavenRepository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/D:/MavenRepository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/D:/MavenRepository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/D:/MavenRepository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/D:/MavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/D:/MavenRepository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/D:/MavenRepository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/D:/MavenRepository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/D:/MavenRepository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/D:/MavenRepository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/MavenRepository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/D:/MavenRepository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/D:/MavenRepository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/D:/MavenRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/MavenRepository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/D:/MavenRepository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/D:/MavenRepository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/D:/MavenRepository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/D:/MavenRepository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/D:/MavenRepository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/D:/MavenRepository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/D:/MavenRepository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/MavenRepository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/D:/MavenRepository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/D:/MavenRepository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/D:/MavenRepository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/MavenRepository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/D:/MavenRepository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/MavenRepository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/D:/MavenRepository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/D:/MavenRepository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/D:/Idea/IntelliJ%20IDEA%202025.1.4.1/lib/idea_rt.jar, file:/C:/Users/<USER>/AppData/Local/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.302",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.genco.admin.GencoAdminApplication" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[prod]' will not be applied" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/application-prod.yml' (classpath:/application-prod.yml) for profile prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.394",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3e2822" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.532",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.533",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.533",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.878",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.585",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.586",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.620",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.620",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.620",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.621",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.621",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.621",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:29.607",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "298 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:30.646",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:30.668",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:30.823",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:30.860",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:36.516",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(8)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:36.546",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(8)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:38.997",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(5)-************",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:57.931",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:57.932",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:58.001",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:58.001",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:59.007",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:59.008",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@56f1e1f9]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:59.066",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:00.009",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:00.011",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5559d64a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:00.013",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:05.610",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:05.611",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:05.773",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=3e0c35242e766e78e3660855a2ad46c2, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.228",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.228",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7fbfb4fc]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.228",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=f314572531e74f07ae6e19bc2c87d804&temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.336",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.336",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2fa47484]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.344",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.360",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.361",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.593",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.593",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@13495cda]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.597",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.620",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.627",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1aeff26c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.631",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.812",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.812",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3204853f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.821",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.903",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.903",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@381645b4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.911",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.944",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.944",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@234ccf00]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.944",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754529792", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754529792", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:13.153",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:13.153",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@25b9e7ba]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:13.163",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:17.853",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:17.860",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@48bdc5e0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:17.869",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.674",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=f314572531e74f07ae6e19bc2c87d804&temp=1754530331", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.691",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.692",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7ebe4155]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.695",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530331", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.713",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.953",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.953",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4de1365b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.956",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.409",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754530332", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754530332", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754530332", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.412",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.412",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754530332", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.412",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.414",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2bd0569]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.649",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.768",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.768",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3442591a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.773",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.867",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.867",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3c4b7373]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.870",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:17.682",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:17.682",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3f862829]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:17.685",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.404",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=f314572531e74f07ae6e19bc2c87d804&temp=1754530351", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.405",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.410",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.410",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1eb53a04]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.413",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.435",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530351", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.678",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.679",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@451366b0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.681",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.079",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754530352", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.079",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754530352", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.080",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.080",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.080",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754530352", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.081",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.083",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754530352", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.084",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.244",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.244",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@18af2f47]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.248",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.290",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.290",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@22ed94f6]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.293",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.314",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.314",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3a88b2aa]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.317",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:34.327",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:34.327",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7d8dfdba]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:34.330",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.194",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=f314572531e74f07ae6e19bc2c87d804&temp=1754530382", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.196",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.200",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.201",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7aeda640]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.204",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.216",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530382", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.217",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@45ff17e5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.328",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.634",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.634",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.634",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.634",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.656",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.656",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2e475856]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.657",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:15.110",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:15.110",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2bf54dc0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:15.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.302",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.302",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.304",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.304",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.323",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.323",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@319718a5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.324",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.790",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.791",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4ff88fce]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.792",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.655",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.655",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2cb579e8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.656",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:00.117",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:00.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4b293264]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:00.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.676",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.676",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7428166f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:07.142",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:07.142",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@52ed191b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:07.142",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:19.820",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:19.822",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:19.827",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=fd9863aa8ef9ff3022b5695ce306eaa3, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.320",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.320",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@31b128d2]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.320",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=9c01e5ed20784ce7a46a4210aae65538&temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3183c296]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.353",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.353",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4845cd22]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7bf9c54c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.909",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.003",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.003",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4b5128ed]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.013",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.138",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.138",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6378c988]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.143",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4e16fd66]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.328",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.400",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754530584", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754530584", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.810",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.810",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@adf449d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.819",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:29.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:29.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@582cb27f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:29.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.554",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=9c01e5ed20784ce7a46a4210aae65538&temp=1754530717", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.555",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.559",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.559",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@78d6d084]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.562",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.580",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530717", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.581",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.818",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.818",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@22c1a775]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.822",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.189",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754530718", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.189",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754530718", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.189",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754530718", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754530718", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.423",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.424",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2eb14635]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.426",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.480",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.480",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@50d8a801]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.483",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.606",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.606",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@530b0410]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.608",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:40.041",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:40.042",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6abc7ceb]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:40.045",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.174",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=9c01e5ed20784ce7a46a4210aae65538&temp=1754530984", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.175",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.179",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.179",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@12913fe5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.181",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.198",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530984", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.199",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.287",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.287",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1ead9636]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.288",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.646",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754530984", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.646",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754530984", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.647",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754530984", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.646",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754530984", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.647",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.650",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.652",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.652",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.733",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.733",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3557e75f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.736",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.958",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.958",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5bdfa32]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.961",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:05.066",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:05.068",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3ef7d15a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:05.069",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:09.923",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:09.923",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5e707518]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:09.929",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.205",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=9c01e5ed20784ce7a46a4210aae65538&temp=1754531014", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.206",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.209",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.209",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@76f5513f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.211",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.234",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754531014", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.236",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.471",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.471",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1f8f854a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.473",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.815",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754531014", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.815",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754531014", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.815",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754531014", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.815",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.815",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754531014", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.815",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.815",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.817",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.914",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.915",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@354afde5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.919",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.979",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.979",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@23392a15]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:34.982",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:35.046",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:35.046",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5432452d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:35.049",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:39.322",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:39.322",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@254c751f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:39.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:53:57.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:53:57.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:53:57.333",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:53:57.337",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:53:57.393",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:53:57.393",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@73db16fd]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:53:57.398",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:53:58.399",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:53:58.399",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4ba95eec]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:53:58.407",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:03.130",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:03.130",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:03.138",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=4ee4b3a8a092e1b50d473092216a1242, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.180",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.180",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5ab9b6c3]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.180",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.205",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754531644", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.205",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=4a090cfc0c324fa59ea655f4d4042468&temp=1754531644", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.205",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.205",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.213",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.213",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6b835285]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.213",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.228",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754531644", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.229",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.418",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.418",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4b271b12]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.423",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.759",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.759",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@8721722]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.761",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.851",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754531644", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.851",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754531644", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.852",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.852",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.852",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754531644", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:04.852",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:05.056",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:05.057",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@8bfe16e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:05.057",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:05.254",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:05.256",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@62692577]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:05.258",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:05.453",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:05.454",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5822d368]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:05.457",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:08.628",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754531648", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:08.629",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:08.629",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754531648", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:08.630",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:09.083",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:09.083",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@434e59af]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:09.088",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:13.066",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:13.066",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@b447cb8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:54:13.072",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.196",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=4a090cfc0c324fa59ea655f4d4042468&temp=1754532783", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.202",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.217",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.218",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2c5c37ca]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.223",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.266",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754532783", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.267",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.384",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.385",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@302c9537]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.388",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.769",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754532783", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.769",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.772",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754532783", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.774",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.776",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754532783", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.777",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754532783", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.777",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.778",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.881",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.881",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6cfc5af8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:03.884",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:04.059",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:04.059",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@518af9e2]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:04.061",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:04.073",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:04.074",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3aa902e5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:04.076",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:09.793",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:09.793",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@cb63a1c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:09.798",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:21.937",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=4a090cfc0c324fa59ea655f4d4042468&temp=1754532801", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:21.938",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:21.943",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:21.943",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@229dbe4d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:21.945",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:21.965",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754532801", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:21.966",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.185",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.185",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@385a9f4d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.187",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.464",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754532802", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.466",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.466",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754532802", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.466",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754532802", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.467",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.467",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.468",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754532802", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.468",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@50cfc4e0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.667",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.698",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.698",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6cf98573]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.699",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.729",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.729",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4581ec3c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:22.732",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:26.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:26.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@26df9e2a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:26.910",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:36.851",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=4a090cfc0c324fa59ea655f4d4042468&temp=1754532816", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:36.851",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:36.853",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:36.853",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@69a86a60]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:36.854",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:36.927",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754532816", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:36.928",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.134",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.134",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3d7cb571]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.135",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.234",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754532817", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.234",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.235",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754532817", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.236",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.240",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754532817", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.248",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754532817", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.250",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.251",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.682",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.682",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.682",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@241dce33]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.684",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@63947b4a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.689",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:37.689",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:38.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:38.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@556f9676]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:38.114",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:39.733",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:39.733",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@484570fe]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:13:39.735",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:00.770",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=4a090cfc0c324fa59ea655f4d4042468&temp=1754532960", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:00.771",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:00.776",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:00.776",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@13b4bf30]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:00.777",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:00.797",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754532960", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:00.798",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:00.889",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:00.889",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@72f1190c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:00.892",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.307",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754532961", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.308",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.324",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754532961", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.325",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754532961", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.327",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754532961", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.407",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.407",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6894b94f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.412",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.660",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.660",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2a034fae]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.665",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.733",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.733",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@42c7421b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:01.736",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:07.325",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:07.325",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@374c9c3e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:07.328",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.030",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=4a090cfc0c324fa59ea655f4d4042468&temp=1754533019", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.032",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.036",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.036",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7d0a00d5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.038",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.100",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754533019", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.101",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.370",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.370",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@ca7b675]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.370",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.462",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754533019", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.462",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754533019", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.462",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.463",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754533019", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.463",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.463",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754533019", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.463",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.464",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.628",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.628",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4acf1671]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.662",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.662",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@57b5763e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.662",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.723",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.723",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@271a2195]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:16:59.723",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:17:01.720",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:17:01.720",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@63a82fb]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:17:01.720",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.063",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=4a090cfc0c324fa59ea655f4d4042468&temp=1754533194", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.064",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.068",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.068",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@628ee2c3]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.071",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.081",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754533194", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.081",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.186",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.186",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5cc5988c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.188",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.290",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754533194", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.291",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.291",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754533194", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.291",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754533194", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.291",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.291",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754533194", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.291",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.292",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.394",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.394",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3ae5c24]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.395",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.564",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.566",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@27d14eac]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.566",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.818",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.818",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@8f35ef7]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:54.819",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:58.865",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:58.866",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@53da7219]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:19:58.869",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:28.284",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=4a090cfc0c324fa59ea655f4d4042468&temp=1754533228", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:28.284",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:28.299",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:28.299",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5a36dea4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:30.791",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:30.805",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754533230", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:30.805",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:31.008",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:31.008",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@754f3d18]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:31.010",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:31.111",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754533231", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:31.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754533231", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:31.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754533231", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:31.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:31.114",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754533231", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:31.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:31.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:37.920",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:38.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:38.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@42d611d3]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:38.120",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:38.120",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2e3f76e2]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:38.121",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:38.122",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:38.181",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:38.181",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@31b8608]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:38.183",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:39.821",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:39.821",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2f877c12]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:20:39.823",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:36.364",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/D:/Java/jre/lib/charsets.jar, file:/D:/Java/jre/lib/deploy.jar, file:/D:/Java/jre/lib/ext/access-bridge-64.jar, file:/D:/Java/jre/lib/ext/cldrdata.jar, file:/D:/Java/jre/lib/ext/dnsns.jar, file:/D:/Java/jre/lib/ext/jaccess.jar, file:/D:/Java/jre/lib/ext/localedata.jar, file:/D:/Java/jre/lib/ext/nashorn.jar, file:/D:/Java/jre/lib/ext/sunec.jar, file:/D:/Java/jre/lib/ext/sunjce_provider.jar, file:/D:/Java/jre/lib/ext/sunmscapi.jar, file:/D:/Java/jre/lib/ext/sunpkcs11.jar, file:/D:/Java/jre/lib/ext/zipfs.jar, file:/D:/Java/jre/lib/javaws.jar, file:/D:/Java/jre/lib/jce.jar, file:/D:/Java/jre/lib/jfr.jar, file:/D:/Java/jre/lib/jsse.jar, file:/D:/Java/jre/lib/management-agent.jar, file:/D:/Java/jre/lib/plugin.jar, file:/D:/Java/jre/lib/resources.jar, file:/D:/Java/jre/lib/rt.jar, file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/, file:/C:/Users/<USER>/Desktop/shop/api/genco-service/target/classes/, file:/C:/Users/<USER>/Desktop/shop/api/genco-common/target/classes/, file:/D:/MavenRepository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/D:/MavenRepository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/D:/MavenRepository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/D:/MavenRepository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/D:/MavenRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/D:/MavenRepository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/D:/MavenRepository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/D:/MavenRepository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/D:/MavenRepository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/MavenRepository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/D:/MavenRepository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/D:/MavenRepository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/D:/MavenRepository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/D:/MavenRepository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/MavenRepository/com/google/guava/guava/20.0/guava-20.0.jar, file:/D:/MavenRepository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/MavenRepository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/D:/MavenRepository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/MavenRepository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/MavenRepository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/D:/MavenRepository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/D:/MavenRepository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/MavenRepository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/MavenRepository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/D:/MavenRepository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/D:/MavenRepository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/D:/MavenRepository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/D:/MavenRepository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/D:/MavenRepository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/D:/MavenRepository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/D:/MavenRepository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/D:/MavenRepository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/D:/MavenRepository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/D:/MavenRepository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/D:/MavenRepository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/D:/MavenRepository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/D:/MavenRepository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/D:/MavenRepository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/D:/MavenRepository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/D:/MavenRepository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/D:/MavenRepository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/D:/MavenRepository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/D:/MavenRepository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/MavenRepository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/D:/MavenRepository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/D:/MavenRepository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/D:/MavenRepository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/D:/MavenRepository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/D:/MavenRepository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/D:/MavenRepository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/D:/MavenRepository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/D:/MavenRepository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/D:/MavenRepository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/D:/MavenRepository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/D:/MavenRepository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/D:/MavenRepository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/D:/MavenRepository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/D:/MavenRepository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/D:/MavenRepository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/D:/MavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/D:/MavenRepository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/D:/MavenRepository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/D:/MavenRepository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/D:/MavenRepository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/D:/MavenRepository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/MavenRepository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/D:/MavenRepository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/D:/MavenRepository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/D:/MavenRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/MavenRepository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/D:/MavenRepository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/D:/MavenRepository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/D:/MavenRepository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/D:/MavenRepository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/D:/MavenRepository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/D:/MavenRepository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/D:/MavenRepository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/MavenRepository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/D:/MavenRepository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/D:/MavenRepository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/D:/MavenRepository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/MavenRepository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/D:/MavenRepository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/MavenRepository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/D:/MavenRepository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/D:/MavenRepository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/D:/Idea/IntelliJ%20IDEA%202025.1.4.1/lib/idea_rt.jar, file:/C:/Users/<USER>/AppData/Local/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:36.650",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.genco.admin.GencoAdminApplication" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:36.805",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:36.805",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:36.805",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[prod]' will not be applied" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:36.805",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/application-prod.yml' (classpath:/application-prod.yml) for profile prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:36.807",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@63f34b70" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:43.095",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:43.095",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:43.095",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:44.656",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:47.880",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:47.881",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:48.017",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:48.017",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:48.017",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:48.023",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:48.023",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:26:48.023",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:27:13.990",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "298 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:27:17.973",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:27:18.011",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:27:18.433",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:27:18.512",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:27:29.573",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(14)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:27:29.652",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(14)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:27:31.872",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(13)-************",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:35.790",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:35.790",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:35.836",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:35.836",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:36.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:36.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6fd1cc5d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:36.464",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:37.553",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:37.553",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6b377a92]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:37.554",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:51.544",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:51.544",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:51.669",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=f52846148395bdb2492df3a087439509, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.877",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.877",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7da03650]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.877",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.944",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=a7b959eaf65a49ddb12fdd0feec15e94&temp=1754534873", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.944",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754534873", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.944",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.944",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.961",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.961",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7cb5f069]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.971",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.978",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754534873", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:53.978",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.200",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.200",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@331e9b45]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.204",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.246",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.246",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@42f4c400]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.251",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754534874", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754534874", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754534874", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.552",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.552",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7c2144e4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.552",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.576",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.576",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@126e42bf]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.576",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.752",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.755",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@57fd5fb2]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:47:54.755",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:29.576",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?keywords=&page=1&limit=20&type=1&isShow=&temp=1754534909", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:29.577",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:31.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754534911", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:31.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754534911", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:31.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:31.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:31.543",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:31.543",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4f17b08f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:31.551",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:34.493",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:34.493",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2777f4d8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:34.502",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:39.484",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:39.484",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@a829df]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:48:39.551",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:11.334",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:11.334",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:11.335",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:11.335",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:11.354",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:11.354",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@13c46c13]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:11.355",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:11.781",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:11.782",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1b9bff15]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:11.783",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:17.385",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=1754535017", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:17.386",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:17.392",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:17.392",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6383c067]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:17.393",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.209",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.209",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.218",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=b035096a909133695460954d71411801, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.693",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.693",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5aa113a3]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.695",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.709",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754535024", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.709",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.709",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=99deba2fefe24c9692dc5e8547fa3f08&temp=1754535024", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.709",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.709",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.709",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5037dcfe]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.717",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.726",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754535024", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.726",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.792",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.792",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@dbbe00f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:24.801",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.215",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.215",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@23d0665d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.218",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.285",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754535025", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.285",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754535025", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.285",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.285",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754535025", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.285",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.286",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.368",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.368",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6349e758]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.376",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.518",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.518",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2dc52eac]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.518",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.708",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.708",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@13ef31be]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:25.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:28.240",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754535028", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:28.240",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754535028", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:28.241",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:28.241",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:28.709",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:28.709",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3539b360]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:28.717",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:33.021",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:33.021",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@69510925]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:50:33.021",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.267",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@63f34b70, started on Thu Aug 07 10:26:36 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.295",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Beans' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.295",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Caches' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.295",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Health' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.296",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Info' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.296",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Conditions' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.296",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Configprops' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.296",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Env' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.296",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Loggers' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.296",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Threaddump' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.296",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Metrics' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.296",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Scheduledtasks' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:52:02.296",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Mappings' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:08.892",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/D:/Java/jre/lib/charsets.jar, file:/D:/Java/jre/lib/deploy.jar, file:/D:/Java/jre/lib/ext/access-bridge-64.jar, file:/D:/Java/jre/lib/ext/cldrdata.jar, file:/D:/Java/jre/lib/ext/dnsns.jar, file:/D:/Java/jre/lib/ext/jaccess.jar, file:/D:/Java/jre/lib/ext/localedata.jar, file:/D:/Java/jre/lib/ext/nashorn.jar, file:/D:/Java/jre/lib/ext/sunec.jar, file:/D:/Java/jre/lib/ext/sunjce_provider.jar, file:/D:/Java/jre/lib/ext/sunmscapi.jar, file:/D:/Java/jre/lib/ext/sunpkcs11.jar, file:/D:/Java/jre/lib/ext/zipfs.jar, file:/D:/Java/jre/lib/javaws.jar, file:/D:/Java/jre/lib/jce.jar, file:/D:/Java/jre/lib/jfr.jar, file:/D:/Java/jre/lib/jsse.jar, file:/D:/Java/jre/lib/management-agent.jar, file:/D:/Java/jre/lib/plugin.jar, file:/D:/Java/jre/lib/resources.jar, file:/D:/Java/jre/lib/rt.jar, file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/, file:/C:/Users/<USER>/Desktop/shop/api/genco-service/target/classes/, file:/C:/Users/<USER>/Desktop/shop/api/genco-common/target/classes/, file:/D:/MavenRepository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/D:/MavenRepository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/D:/MavenRepository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/D:/MavenRepository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/D:/MavenRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/D:/MavenRepository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/D:/MavenRepository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/D:/MavenRepository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/D:/MavenRepository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/MavenRepository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/D:/MavenRepository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/D:/MavenRepository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/D:/MavenRepository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/D:/MavenRepository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/MavenRepository/com/google/guava/guava/20.0/guava-20.0.jar, file:/D:/MavenRepository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/MavenRepository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/D:/MavenRepository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/MavenRepository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/MavenRepository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/D:/MavenRepository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/D:/MavenRepository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/MavenRepository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/MavenRepository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/D:/MavenRepository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/D:/MavenRepository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/D:/MavenRepository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/D:/MavenRepository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/D:/MavenRepository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/D:/MavenRepository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/D:/MavenRepository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/D:/MavenRepository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/D:/MavenRepository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/D:/MavenRepository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/D:/MavenRepository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/D:/MavenRepository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/D:/MavenRepository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/D:/MavenRepository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/D:/MavenRepository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/D:/MavenRepository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/D:/MavenRepository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/D:/MavenRepository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/D:/MavenRepository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/MavenRepository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/D:/MavenRepository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/D:/MavenRepository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/D:/MavenRepository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/D:/MavenRepository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/D:/MavenRepository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/D:/MavenRepository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/D:/MavenRepository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/D:/MavenRepository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/D:/MavenRepository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/D:/MavenRepository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/D:/MavenRepository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/D:/MavenRepository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/D:/MavenRepository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/D:/MavenRepository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/D:/MavenRepository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/D:/MavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/D:/MavenRepository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/D:/MavenRepository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/D:/MavenRepository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/D:/MavenRepository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/D:/MavenRepository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/MavenRepository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/D:/MavenRepository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/D:/MavenRepository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/D:/MavenRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/MavenRepository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/D:/MavenRepository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/D:/MavenRepository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/D:/MavenRepository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/D:/MavenRepository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/D:/MavenRepository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/D:/MavenRepository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/D:/MavenRepository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/MavenRepository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/D:/MavenRepository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/D:/MavenRepository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/D:/MavenRepository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/MavenRepository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/D:/MavenRepository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/MavenRepository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/D:/MavenRepository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/D:/MavenRepository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/D:/Idea/IntelliJ%20IDEA%202025.1.4.1/lib/idea_rt.jar, file:/C:/Users/<USER>/AppData/Local/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:08.991",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.genco.admin.GencoAdminApplication" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:09.048",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:09.049",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:09.049",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[prod]' will not be applied" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:09.049",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/application-prod.yml' (classpath:/application-prod.yml) for profile prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:09.050",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5c1bd44c" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:12.121",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:12.121",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:12.121",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:12.525",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:13.416",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:13.417",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:13.445",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:13.452",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:13.452",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:13.453",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:13.453",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:13.453",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:21.852",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "298 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:22.871",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:22.895",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:23.052",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:23.090",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:28.010",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(5)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:28.038",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(5)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 10:55:29.843",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(6)-************",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:11:48.783",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:11:48.783",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:11:48.850",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:11:48.850",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:11:50.000",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:11:50.001",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2b0dda49]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:11:50.027",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:11:50.258",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:11:50.258",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5af28ac4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:11:50.260",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:12:02.670",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:12:02.670",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:12:02.670",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:12:02.675",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:12:02.679",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:12:02.679",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@22018507]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:12:02.679",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:12:03.086",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:12:03.086",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2b955cba]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:12:03.100",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:26.876",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:26.876",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:26.879",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:26.879",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:26.888",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:26.888",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@231ee6a5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:26.889",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:27.305",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:27.305",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@32246609]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:27.305",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:38.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:38.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:38.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:38.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:38.428",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:38.428",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5313d724]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:38.428",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:38.839",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:38.839",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4137fb2f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:13:38.839",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:08.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:08.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:08.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:08.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:08.674",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:08.674",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4752a5b0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:08.674",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:09.106",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:09.106",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@296c2d03]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:09.106",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:52.860",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:52.860",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:52.867",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:52.867",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:52.876",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:52.877",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5d3d470]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:52.877",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:53.301",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:53.301",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5eb7d42b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:14:53.301",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:39.541",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:39.541",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:39.705",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=c9ec3dfa0e8d022133f871e48e6d40cc, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.538",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.538",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@********]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.539",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.622",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=aa61897af8714f048c52b6857da19d37&temp=1754536541", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.622",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754536541", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.622",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.622",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.647",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.647",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5f8e600]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.662",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.671",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754536541", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.672",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.848",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.848",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4e7311a1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.854",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.913",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.913",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@53ebcc29]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.917",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.926",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754536541", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.926",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754536541", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.926",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.926",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.926",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754536541", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:41.926",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:42.022",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:42.022",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7893a330]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:42.022",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:42.022",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@446a31a7]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:42.022",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:42.022",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:42.105",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:42.105",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@638ce769]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:42.107",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:46.509",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754536546", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:46.509",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754536546", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:46.509",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:46.509",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:46.921",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:46.929",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@542db6f9]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:46.938",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:48.780",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:48.780",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7019511b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 11:15:48.780",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    

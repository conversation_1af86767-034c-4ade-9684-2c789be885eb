{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\i18n.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\i18n.js", "mtime": 1754536415950}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _vue = _interopRequireDefault(require(\"vue\"));\nvar _vueI18n = _interopRequireDefault(require(\"vue-i18n\"));\nvar _zhCN = _interopRequireDefault(require(\"./lang/zh-CN\"));\nvar _en = _interopRequireDefault(require(\"./lang/en\"));\nvar _id = _interopRequireDefault(require(\"./lang/id\"));\nvar _zhCN2 = _interopRequireDefault(require(\"element-ui/lib/locale/lang/zh-CN\"));\nvar _en2 = _interopRequireDefault(require(\"element-ui/lib/locale/lang/en\"));\nvar _id2 = _interopRequireDefault(require(\"element-ui/lib/locale/lang/id\"));\nvar _locale = _interopRequireDefault(require(\"element-ui/lib/locale\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n// Element UI 语言包\n\n_vue.default.use(_vueI18n.default);\nvar messages = {\n  'zh-CN': _zhCN.default,\n  en: _en.default,\n  id: _id.default\n};\n\n// Element UI 语言包映射\nvar elementLocales = {\n  'zh-CN': _zhCN2.default,\n  'en': _en2.default,\n  'id': _id2.default\n};\nvar i18n = new _vueI18n.default({\n  locale: localStorage.getItem('locale') || 'zh-CN',\n  fallbackLocale: 'zh-CN',\n  messages: messages\n});\n\n// 设置 Element UI 初始语言\nvar currentLocale = localStorage.getItem('locale') || 'zh-CN';\n_locale.default.use(elementLocales[currentLocale]);\n\n// 监听语言变化，同步更新 Element UI 语言\ni18n.vm.$watch('locale', function (newLocale) {\n  if (elementLocales[newLocale]) {\n    _locale.default.use(elementLocales[newLocale]);\n  }\n});\nvar _default = exports.default = i18n;", null]}